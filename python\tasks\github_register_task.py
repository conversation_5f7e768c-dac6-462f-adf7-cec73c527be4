import logging
import os
from pathlib import Path
import re
import signal
import sys
from typing import Union, List, Dict, Set
import aiofiles
import asyncio
import random
from datetime import datetime
from camoufox.async_api import Async<PERSON>amoufox
from playwright.async_api import (
    Page,
    Frame,
    Request,
    Route,
)
from utils.cache_helper import handle_route, setup_page_caching
from utils.config_manager import ConfigManager
from utils.user_generator import UserGenerator
from utils.captcha_helper import <PERSON><PERSON><PERSON>el<PERSON>
from utils.msmail import accounts, get_email_content
from utils.browser_fingerprint import BrowserFingerprint
from urllib.parse import urlparse, parse_qs


# 定义自定义异常类
class IPBadError(Exception):
    """自定义的异常类"""

    def __init__(self, message="IPBadError"):
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        return f"IPBadError: {self.message}"


class GitHubRegisterTask:
    """
    GitHub注册任务
    """

    def __init__(
        self,
        config_manager: ConfigManager,
        account_file: Union[str, Path] = "data/github_accounts.csv",
        error_file: Union[str, Path] = "data/github_error_accounts.txt",
    ) -> None:
        """
        初始化配置管理器

        Args:
            config_manager: 配置管理
            account_file: 账号文件
            error_file: 注册失败的账号文件
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.error_file = Path(error_file)
        self.user_generator = UserGenerator(config_manager)
        self.browser_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.file_lock = asyncio.Lock()
        self.fingerprint_generator = BrowserFingerprint()
        self.captcha_helper = CaptchaHelper(
            two_captcha_key=self.config_manager.two_captcha_key,
            captcharun_token=self.config_manager.captcharun_token,
            browser_user_agent=self.browser_user_agent,
        )

    async def get_registered_emails(self) -> Set[str]:
        """
        读取已经注册的账号，避免重复注册

        Returns:
            Set[str]: 已注册的邮箱集合
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 检查CSV文件是否存在
            if not self.account_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.account_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return set()

            # 读取并解析CSV文件
            async with aiofiles.open(self.account_file, "r", encoding="utf-8") as f:
                content = await f.read()

            # 提取邮箱（CSV的第一列）
            emails = set()
            for line in content.strip().split("\n"):
                if line.strip():
                    email = line.split(",")[0]
                    emails.add(email.strip())

            self.logger.info(f"已从{self.account_file}读取{len(emails)}个已注册账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取已注册账号时出错: {str(e)}")
            return set()

    async def get_error_emails(self) -> List[str]:
        """
        读取注册失败的错误账号，避免重复注册

        Returns:
            List[str]: 注册失败的邮箱列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            # 检查错误账号文件是否存在
            if not self.error_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.error_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return []

            # 读取并解析文件
            async with aiofiles.open(self.error_file, "r", encoding="utf-8") as f:
                content = await f.read()

            emails = [
                line.strip() for line in content.strip().split("\n") if line.strip()
            ]

            self.logger.info(f"已从{self.error_file}读取{len(emails)}个注册失败的账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取注册失败账号时出错: {str(e)}")
            return []

    async def save_error_account(self, email: str) -> None:
        """
        保存注册失败的账号到文件

        Args:
            email: 注册失败的邮箱
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            async with self.file_lock:
                async with aiofiles.open(self.error_file, "a", encoding="utf-8") as f:
                    await f.write(f"{email}\n")
            self.logger.info(f"成功保存注册失败账号 {email} 到 {self.error_file}")
        except Exception as e:
            self.logger.error(f"保存注册失败账号时出错: {str(e)}")

    async def register_single_account(self, email: str, page: Page):
        """
        注册单个GitHub账号

        Args:
            email: 邮箱地址
            page: 浏览器页面对象

        Returns:
            bool: 注册是否成功
            str: 用户名
        """
        
        self.logger.info(f"开始注册GitHub账号: {email}")

        # 提取邮箱前缀作为用户名
        email_prefix = email.split("@")[0]
        password = "Dean0104@github"
        username = email_prefix
        
        try:
            # 打开GitHub注册页面
            await page.goto("https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home", timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 模拟人类行为
           # await self.fingerprint_generator.simulate_human_behavior(page)

            # 人性化填写邮箱
            await self.fingerprint_generator.human_like_fill(page, 'input[name="user[email]"]', email)

            # 模拟人类行为
            #await self.fingerprint_generator.simulate_human_behavior(page)

            # 人性化填写密码
            await self.fingerprint_generator.human_like_fill(page, 'input[name="user[password]"]', password)

            # 模拟人类行为
           # await self.fingerprint_generator.simulate_human_behavior(page)

            # 人性化填写用户名
            await self.fingerprint_generator.human_like_fill(page, 'input[name="user[login]"]', username)
       
            await page.click('input[name="user[email]"]',timeout=5000)
            await page.wait_for_timeout(3000)
            await page.click('button[aria-describedby="terms-of-service"]',timeout=5000)
          
            
            # 等待用户手动操作完成
            # loop = asyncio.get_running_loop()
            # self.logger.info("请进行必要的手动操作...")
            # await loop.run_in_executor(None, input, "完成手动操作后，请在命令行按回车键继续...")

            # try:
            #     continue_button = page.locator('button[aria-describedby="terms-of-service"].signup-form-fields__button:has-text("Continue")')
            #     # 确保按钮已加载并可见
            #     await continue_button.wait_for(state="visible")
            #     # 点击按钮
            #     await continue_button.click()
            #     # 等待一段时间以查看结果
            #     await page.wait_for_timeout(5000)

            # except Exception as e:
            #     self.logger.info(f"点击继续按钮失败: {str(e)}")

            # try:
            #     button_selector = 'button[data-theme="home.verifyButton"]'
            #     # 等待按钮可见并可点击
            #     await page.wait_for_selector(button_selector, state="visible", timeout=10000)
            #     # 点击按钮
            #     await page.click(button_selector)
            # except Exception as e:
            #     self.logger.info(f"点击视觉挑战失败: {str(e)}")

            
            # 获得sitekey
            # enforcement_frame = page.frame_locator(
            #     'iframe[title="Please verify by completing this captcha."]'
            # )  # 外部的iframe
            # verification_challenge = enforcement_frame.frame_locator(
            #     "iframe"
            # )  # 再找到iframe内的iframe
            # fc_token_input = verification_challenge.locator("#FunCaptcha-Token").first
            # fc_token = await fc_token_input.input_value()
            # self.logger.info(f"fc_token:{fc_token}")
            # params = dict(
            #     param.split("=", 1) for param in fc_token.split("|") if "=" in param
            # )
            # sitekey = params.get("pk", "")
            # surl = params.get("surl", "").replace("%2F", "/").replace("%3A", ":")
            # # 获得url中data值
            # enforcementFrameSrc = await page.locator(
            #     'iframe[title="Please verify by completing this captcha."]'
            # ).get_attribute("src")
            # parsed_url = urlparse(enforcementFrameSrc)
            # query_params = parse_qs(parsed_url.query)
            # data_value = query_params.get("data", [""])[0]
            # self.logger.info(f"开始识别验证码:url:{page.url},sitekey:{sitekey}.surl:{surl}.data_value:{data_value}")
            
            # # siteReferer=https://github.com/signup 
            # code = self.captcha_helper.get_captcharun_token(
            #     site_key=sitekey, blob=data_value, surl=surl,siteReferer="https://octocaptcha.com"
            # )
            # # code = await self.captcha_helper.get_two_captcha_token_v2(
            # #     site_key=sitekey, blob=data_value, surl=surl,siteReferer="https://octocaptcha.com"
            # # )
            # self.logger.info(f"token:{str(code)}")
            # # 构造JavaScript代码字符串,注入脚本
            # script = """
            # (token) => {
            #     console.log('token:',token)
            #     // 这里的代码在 iframe 的上下文中运行
            #     let script = document.createElement('SCRIPT');
            #     script.append('function captchaSubmit(token) { parent.postMessage(JSON.stringify({ eventId: "challenge-complete", payload: { sessionToken: token } }), "*") }');
            #     document.documentElement.appendChild(script);
            #     captchaSubmit(token); // 直接调用函数
            # }
            # """
            # frame: Frame = page.frame(url=enforcementFrameSrc)
            # await frame.evaluate(script, code)


            await page.wait_for_url(
                "https://github.com/account_verifications", timeout=60000
            )

            self.logger.info(f"等待接收确认邮件: {email}")
            max_retries = 30
            email_content = ""

            for i in range(max_retries):
                try:
                    email_content = await get_email_content(email)
                    if (
                        "Here's your GitHub launch code!" in email_content
                    ):
                        break
                except Exception as e:
                    self.logger.warning(f"第{i+1}次获取邮件失败: {str(e)}")

                if i < max_retries - 1:
                    self.logger.info(f"等待5秒后重试...")
                    await page.wait_for_timeout(5000)

            if  email_content:
                # 提取验证码
                verification_code_match = re.search(r'(\d{8})', email_content)
                if not verification_code_match:
                    raise Exception(f"无法从邮件中提取验证码: {email}")

                verification_code = verification_code_match.group(1)
                self.logger.info(f"提取到验证码: {verification_code}")        # 提取确认链接
                confirmation_url_match = re.search(
                    r'(https://github\.com/account_verifications/confirm/[\w-]+/\d+)', email_content
                )

                # 检查是否有确认链接
                if confirmation_url_match:
                    confirmation_url = confirmation_url_match.group(1)
                    self.logger.info(f"提取到确认链接: {confirmation_url}")

                    # 打开确认链接
                    await page.goto(confirmation_url, wait_until="load", timeout=60000)

                    # 等待页面加载完成
                    await page.wait_for_timeout(2000)
                    
                else:
                    # 如果没有确认链接，则使用验证码
                    self.logger.info(f"未找到确认链接: {email_content}，将使用验证码: {verification_code}")

                    # 填写验证码
                    code_fill_wait = 100
                    await page.fill("input#launch-code-0", verification_code[0])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-1", verification_code[1])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-2", verification_code[2])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-3", verification_code[3])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-4", verification_code[4])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-5", verification_code[5])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-6", verification_code[6])
                    await page.wait_for_timeout(code_fill_wait)
                    await page.fill("input#launch-code-7", verification_code[7])
                    

                    # 点击提交按钮 点击按钮会失败
                    # await page.click("form > button.width-full")

            # 等待注册完成，可能会跳转到欢迎页面或验证邮箱页面
            await page.wait_for_url("https://github.com/**", timeout=30000)
            
            # 开始登录
            # 填写邮箱
            await page.fill('input[name="login"]', email)

            # 填写密码
            await page.fill('input[name="password"]', password)
            
            await page.click('input[name="commit"]')
            
            await page.wait_for_timeout(5000)
            
            self.logger.info(f"GitHub账号注册成功: {email}")
            return True, username
        except Exception as e:
            self.logger.error(f"注册失败或超时: {str(e)}")
            # 暂时不保存
            await self.save_error_account(email)
            return False, username

    async def get_available_accounts(self) -> List[Dict]:
        """
        从msmail.py中获取可用的邮箱（排除已注册的和注册失败的）

        Returns:
            List[Dict]: 可用的账号列表
        """
        # 获取已注册的账号
        registered_emails = await self.get_registered_emails()
        # 获取注册失败的账号
        error_emails = await self.get_error_emails()

        # 过滤出未注册的账号且不在错误邮箱列表中的账号
        available_accounts = []
        for account in accounts:
            email = account.get("email")
            if email and email not in registered_emails and email not in error_emails:
                available_accounts.append(account)

        self.logger.info(f"找到{len(available_accounts)}个未注册的账号")
        return available_accounts

    async def register_accounts(
        self,
        register_count: int = 5,
    ):
        """注册多个账号"""
        registered_users = []

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        # 开始注册账号 - 为每个账号生成新的浏览器指纹
        for i in range(min(register_count, len(available_accounts))):
            account = available_accounts[i]
            email = account.get("email")

            # 为每个账号生成新的浏览器指纹
            fingerprint = self.fingerprint_generator.generate_fingerprint()
            self.logger.info(f"为账号 {email} 生成新的浏览器指纹: UA={fingerprint['user_agent'][:50]}..., "
                            f"视口={fingerprint['viewport']}, 时区={fingerprint['timezone_id']}")

            # 获取camoufox配置选项
            camoufox_options = self.fingerprint_generator.get_camoufox_options(fingerprint)

            # 启动camoufox浏览器
            async with AsyncCamoufox(**camoufox_options) as browser:
                # 创建页面
                page = await browser.new_page()

                try:
                    # 检查当前IP
                    ip_text_content = ""
                    try:
                        await page.goto(
                            "https://myip.ipip.net/", wait_until="load", timeout=60000
                        )
                        ip_text_content = await page.text_content("body")
                        ip_text_content = ip_text_content.strip()
                        self.logger.info(f"Current IP Address: {ip_text_content}")
                    except Exception as e:
                        self.logger.error(f"Failed to check IP: {str(e)}")

                    self.logger.info(
                        f"Registering account {i+1}/{register_count}: {email}"
                    )

                    # 注册账号
                    success, username = await self.register_single_account(email, page)

                    if success:
                        # 保存账号信息
                        await self.save(
                            email, username, "Dean0104@github", ip_text_content
                        )
                        registered_users.append(email)
                        self.logger.info(
                            f"GitHub account {i+1} Created Successfully: {email}"
                        )

                    # 等待下一次注册
                    if i < min(register_count, len(available_accounts)) - 1:
                        wait_time = 5
                        self.logger.info(
                            f"Waiting {wait_time} seconds before registering the next account..."
                        )
                        await page.wait_for_timeout(wait_time * 1000)

                except IPBadError as e:
                    self.logger.error(f"IP被封禁，停止注册: {str(e)}")
                    break
                except Exception as e:
                    self.logger.error(
                        f"Registration failed for account {i+1}: {str(e)}"
                    )
                    await page.wait_for_timeout(5000)  # 等待一段时间再继续

        return registered_users

    async def save(self, email: str, username: str, password: str, ip_address: str = ""):
        """
        保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作

        Args:
            email: 邮箱地址
            username: 用户名
            password: 密码
            ip_address: IP地址
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 获取当前时间
            registration_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(
                    self.account_file, "a+", encoding="utf-8"
                ) as f:
                    user_info = f"{email},{password},{username},{registration_time},{ip_address}\n"
                    await f.write(user_info)
            self.logger.info(f"成功保存账号 {email} 到 {self.account_file}")
        except Exception as e:
            self.logger.error(f"Failed to save user {email}: {str(e)}")

    async def do(
        self,
        register_count: int = 5,
        max_concurrent_tasks: int = 1,
    ):
        """执行注册任务"""
        registered_users = []
        success_count = 0
        sem = asyncio.Semaphore(max_concurrent_tasks)

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        self.logger.info(f"找到 {len(available_accounts)} 个可用账号")

        async def register_task(task_id: str):
            nonlocal success_count
            self.logger.info(f"Starting task {task_id}")

            try:
                async with sem:
                    users = await self.register_accounts(register_count)
                    registered_users.extend(users)
                    success_count += len(users)
                    self.logger.info(
                        f"Task {task_id}: Registered {len(users)}/{register_count}"
                    )
                    return users
            except asyncio.CancelledError:
                self.logger.info(f"Task {task_id} was cancelled")
                raise  # 传播取消信号以确保资源清理
            except Exception as e:
                self.logger.error(f"Task {task_id} failed: {str(e)}")
                return []

        # 创建任务列表
        tasks_def = ["main_task"]
        total_planned = register_count * len(tasks_def)

        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == "win32":
            # Windows使用默认信号处理
            signal.signal(
                signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint)
            )
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)

        try:
            # 创建并跟踪所有任务
            tasks = [
                loop.create_task(register_task(tid)) for tid in tasks_def
            ]
            await asyncio.gather(*tasks, return_exceptions=False)
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != "win32":
                loop.remove_signal_handler(signal.SIGINT)

        self.logger.info(f"Completed: {success_count}/{total_planned}")
        return registered_users
